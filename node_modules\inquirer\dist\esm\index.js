/**
 * Inquirer.js
 * A collection of common interactive command line user interfaces.
 */
import { input, select, number, confirm, rawlist, expand, checkbox, password, editor, search, Separator, } from '@inquirer/prompts';
import PromptsRunner from './ui/prompt.js';
const builtInPrompts = {
    input,
    select,
    /** @deprecated `list` is now named `select` */
    list: select,
    number,
    confirm,
    rawlist,
    expand,
    checkbox,
    password,
    editor,
    search,
};
/**
 * Create a new self-contained prompt module.
 */
export function createPromptModule(opt) {
    function promptModule(questions, answers) {
        const runner = new PromptsRunner(promptModule.prompts, opt);
        const promptPromise = runner.run(questions, answers);
        return Object.assign(promptPromise, { ui: runner });
    }
    promptModule.prompts = { ...builtInPrompts };
    /**
     * Register a prompt type
     */
    promptModule.registerPrompt = function (name, prompt) {
        promptModule.prompts[name] = prompt;
        return this;
    };
    /**
     * Register the defaults provider prompts
     */
    promptModule.restoreDefaultPrompts = function () {
        promptModule.prompts = { ...builtInPrompts };
    };
    return promptModule;
}
/**
 * Public CLI helper interface
 */
const prompt = createPromptModule();
// Expose helper functions on the top level for easiest usage by common users
function registerPrompt(name, newPrompt) {
    prompt.registerPrompt(name, newPrompt);
}
function restoreDefaultPrompts() {
    prompt.restoreDefaultPrompts();
}
const inquirer = {
    prompt,
    ui: {
        Prompt: PromptsRunner,
    },
    createPromptModule,
    registerPrompt,
    restoreDefaultPrompts,
    Separator,
};
export default inquirer;
