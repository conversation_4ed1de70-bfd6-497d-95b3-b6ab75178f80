#!/usr/bin/env node

import chalk from 'chalk';
import inquirer from 'inquirer';
import figlet from 'figlet';
import { execa } from 'execa';
import { promises as fs } from 'fs';
import path from 'path';
import ora from 'ora';

const args = process.argv.slice(2);
const projectDir = args[0];

if (!projectDir) {
  console.error(chalk.red('Error: Please provide a project directory. Usage: npm create cli-test <project-name>'));
  process.exit(1);
}

function formatMessage(message) {
  return chalk.green(`ℹ ${message}`);
}

function printAsciiArt() {
  const art = figlet.textSync('Hiworld', {
    font: 'Standard',
    horizontalLayout: 'default',
    verticalLayout: 'default',
  });
  console.log(chalk.cyan(art));
}

console.clear();
printAsciiArt();
console.log(formatMessage('Welcome to Hiworld Vite!'));
console.log('');

inquirer
  .prompt([
    {
      type: 'checkbox',
      name: 'features',
      message: 'Which features would you like to install and configure? (Select multiple with spacebar)',
      choices: [
        { name: 'Pinia - Vue state management library', value: 'pinia' },
        { name: 'Vue Router - Official routing solution for Vue', value: 'vueRouter' },
        { name: 'Tailwind CSS - Utility-first CSS framework', value: 'tailwind' },
        { name: 'Axios - Promise-based HTTP client', value: 'axios' },
        { name: 'Dexie.js - Minimal IndexedDB wrapper', value: 'dexie' },
      ],
      default: [],
      prefix: '❯',
    },
  ])
  .then(async (answers) => {
    console.clear();
    printAsciiArt();
    const resolvedProjectDir = path.resolve(process.cwd(), projectDir);
    const spinner = ora({
      spinner: 'dots',
      color: 'cyan',
    });

    spinner.start(formatMessage(`Initializing Vite + Vue project in ${projectDir}...`));
    await execa('npm', ['create', 'vite@latest', projectDir, '--', '--template', 'vue'], { stdio: 'pipe' });
    spinner.text = formatMessage('Vite + Vue project initialized');

    process.chdir(resolvedProjectDir);

    const dependencies = ['vue'];
    const devDependencies = [];

    if (answers.features.includes('pinia')) dependencies.push('pinia');
    if (answers.features.includes('vueRouter')) dependencies.push('vue-router@4');
    if (answers.features.includes('tailwind')) devDependencies.push('tailwindcss', '@tailwindcss/vite');
    if (answers.features.includes('axios')) dependencies.push('axios');
    if (answers.features.includes('dexie')) dependencies.push('dexie');

    if (dependencies.length > 1) {
      spinner.start(formatMessage('Installing dependencies...'));
      await execa('npm', ['install', ...dependencies], { stdio: 'pipe' });
      spinner.text = formatMessage('Dependencies installed');
    }

    if (devDependencies.length > 0) {
      spinner.start(formatMessage('Installing dev dependencies...'));
      await execa('npm', ['install', '-D', ...devDependencies], { stdio: 'pipe' });
      spinner.text = formatMessage('Dev dependencies installed');
    }

    spinner.start(formatMessage('Configuring project...'));
    await configureProject(answers, resolvedProjectDir);
    spinner.stop();

    console.log(formatMessage('Done! Navigate to your project:'));
    console.log(`   cd ${projectDir}`);
    console.log('   npm run dev');
  })
  .catch((error) => {
    console.error(chalk.red('Error:', error));
  });

async function configureProject(answers, projectDir) {
  const srcDir = path.join(projectDir, 'src');

  if (answers.features.includes('vueRouter')) {
    await fs.mkdir(path.join(srcDir, 'router'), { recursive: true });
    await fs.writeFile(
      path.join(srcDir, 'router', 'index.js'),
      `import { createRouter, createWebHistory } from 'vue-router';
import Home from '../views/Home.vue';

const routes = [
  { path: '/', name: 'Home', component: Home },
  { path: '/about', name: 'About', component: () => import('../views/About.vue') },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
`
    );

    await fs.mkdir(path.join(srcDir, 'views'), { recursive: true });
    let helloWorldPath = path.join(srcDir, 'components', 'HelloWorld.vue');
    let homeContent = await fs.readFile(helloWorldPath, 'utf-8');

    if (answers.features.includes('tailwind')) {
      let homeVueContent;
      if (answers.features.includes('dexie')) {
        homeVueContent = `<script setup>
import { ref, onMounted } from 'vue'
import db from '../db/database'

const msg = ref('Welcome to Hiworld Vite')
const count = ref(0)
const counterId = ref(1)

onMounted(async () => {
  const counters = await db.counter.toArray()
  if (counters.length > 0) {
    const counter = counters[0]
    count.value = counter.count
    counterId.value = counter.id
  } else {
    counterId.value = await db.counter.add({ count: 0 })
  }
})

const increment = async () => {
  count.value++
  await db.counter.update(counterId.value, { count: count.value })
}

const reset = async () => {
  count.value = 0
  await db.counter.update(counterId.value, { count: 0 })
}
</script>

<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="px-4 py-8">
      <div class="text-center min-w-5xl max-w-5xl mx-auto">
        <div class="flex justify-center items-center">
          <span class="text-4xl font-extrabold bg-gradient-to-r from-indigo-500 from-10% via-sky-500 via-30% to-emerald-500 to-90% bg-clip-text text-transparent">{{ msg }}</span>
        </div>
        <p class="text-gray-600 my-10">This is your homepage created with Vue 3 + Vite</p>
        
        <div class="bg-white rounded-4xl p-10 max-w-xl mx-auto drop-shadow-xl">
          <button
            class="px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors mx-2 min-w-40"
            @click="increment"
          >
            Count is: {{ count }}
          </button>
          <button
            class="px-4 py-2 text-white rounded-full transition-colors mx-2"
            @click="reset"
            :disabled="count === 0"
            :class="count === 0 ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-500 hover:bg-red-600'"
          >
            Reset
          </button>
          <div class="mt-10 text-gray-600">
            Edit <code class="bg-gray-100 px-1 rounded">views/Home.vue</code> to test hot module replacement
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
`;
      } else {
        homeVueContent = `<script setup>
import { ref } from 'vue'
import { useCounterStore } from '../stores/counter'

const msg = ref('Welcome to Hiworld Vite')
const store = useCounterStore()
const count = ref(store.count)

const increment = () => {
  store.increment()
  count.value = store.count
}

const reset = () => {
  store.count = 0
  count.value = 0
}
</script>

<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <div class="px-4 py-8">
      <div class="text-center min-w-5xl max-w-5xl mx-auto">
        <div class="flex justify-center items-center">
          <span class="text-4xl font-extrabold bg-gradient-to-r from-indigo-500 from-10% via-sky-500 via-30% to-emerald-500 to-90% bg-clip-text text-transparent">{{ msg }}</span>
        </div>
        <p class="text-gray-600 my-10">This is your homepage created with Vue 3 + Vite</p>
        
        <div class="bg-white rounded-4xl p-10 max-w-xl mx-auto drop-shadow-xl">
          <button
            class="px-6 py-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors mx-2 min-w-40"
            @click="increment"
          >
            Count is: {{ count }}
          </button>
          <button
            class="px-4 py-2 text-white rounded-full transition-colors mx-2"
            @click="reset"
            :disabled="count === 0"
            :class="count === 0 ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-500 hover:bg-red-600'"
          >
            Reset
          </button>
          <div class="mt-10 text-gray-600">
            Edit <code class="bg-gray-100 px-1 rounded">views/Home.vue</code> to test hot module replacement
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
`;
      }
      await fs.writeFile(path.join(srcDir, 'views', 'Home.vue'), homeVueContent);
    } else {
      await fs.writeFile(
        path.join(srcDir, 'views', 'Home.vue'),
        `<script setup>
import { ref } from 'vue'

const msg = ref('Welcome to Hiworld Vite')
const count = ref(0)

const increment = () => {
  count.value++
}

const reset = () => {
  count.value = 0
}
</script>

<template>
  <div class="home">
    <h1>{{ msg }}</h1>
    <p>This is your homepage created with Vue 3 + Vite</p>
    <div class="card">
      <button @click="increment">
        Count is: {{ count }}
      </button>
      <button @click="reset" :disabled="count === 0">
        Reset
      </button>
      <p>Edit <code>views/Home.vue</code> to test hot module replacement</p>
    </div>
  </div>
</template>

<style scoped>
.home { text-align: center; padding: 2rem; }
h1 { font-size: 2.5rem; color: #2c3e50; margin-bottom: 1rem; }
.card { padding: 2rem; max-width: 500px; margin: 0 auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
button { padding: 0.6em 1.2em; background-color: #4f46e5; color: white; border: none; border-radius: 4px; cursor: pointer; transition: background-color 0.3s; }
button:hover { background-color: #4338ca; }
button:disabled { background-color: #a0aec0; cursor: not-allowed; }
code { background-color: #f1f1f1; padding: 2px 4px; border-radius: 4px; font-family: monospace; }
</style>
`
      );
    }

    await fs.writeFile(
      path.join(srcDir, 'views', 'About.vue'),
      `<template>
  <div>About Page</div>
</template>
`
    );

    await updateMainJs(srcDir, "import router from './router';\n", "app.use(router);");
    await fs.writeFile(
      path.join(srcDir, 'App.vue'),
      `<template>
  <router-view />
</template>
`
    );

    try {
      await fs.unlink(helloWorldPath);
    } catch (err) {
      console.log('Warning: Could not delete HelloWorld.vue');
    }
  }

  if (answers.features.includes('tailwind')) {
    await fs.writeFile(
      path.join(projectDir, 'tailwind.config.js'),
      `/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: { extend: {} },
  plugins: [],
};
`
    );

    if (!answers.features.includes('vueRouter')) {
      await fs.writeFile(
        path.join(srcDir, 'App.vue'),
        `<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center">
    <h1 class="text-4xl font-bold text-blue-600">{{ msg }}</h1>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const msg = ref('Hello, Hiworld Vite!');
</script>
`
      );
    }

    await fs.writeFile(
      path.join(projectDir, 'vite.config.js'),
      `import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [vue(), tailwindcss()],
  server: {
    proxy: {
      '/api': {
        target: 'https://api.example.com/api',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\\/api\\//, ''),
      },
    },
    open: true
  }
});
`
    );

    const stylePath = path.join(srcDir, 'style.css');
    await fs.writeFile(stylePath, `@import "tailwindcss";\n`);
    await updateMainJs(srcDir, "import './style.css';\n", "");
  }

  if (answers.features.includes('pinia')) {
    await fs.mkdir(path.join(srcDir, 'stores'), { recursive: true });
    await fs.writeFile(
      path.join(srcDir, 'stores', 'counter.js'),
      `import { defineStore } from 'pinia';

export const useCounterStore = defineStore('counter', {
  state: () => ({ count: 0 }),
  actions: {
    increment() { this.count++; },
  },
});
`
    );
    await updateMainJs(srcDir, "import { createPinia } from 'pinia';\n", "app.use(createPinia());");
  }

  if (answers.features.includes('axios')) {
    await fs.mkdir(path.join(srcDir, 'services'), { recursive: true });
    await fs.writeFile(
      path.join(srcDir, 'services', 'api.js'),
      `import axios from 'axios';

const api = axios.create({
  baseURL: 'https://api.example.com/api',
  timeout: 15000,
  headers: { 'Content-Type': 'application/json' },
});

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) config.headers.Authorization = 'Bearer ' + token;
    return config;
  },
  (error) => Promise.reject(error)
);

api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          localStorage.removeItem('token');
          window.location.href = '/login';
          break;
        case 403:
          console.error('没有权限访问该资源');
          break;
        case 404:
          console.error('请求的资源不存在');
          break;
        case 500:
          console.error('服务器错误');
          break;
        default:
          console.error('请求失败:', error.response.data);
      }
    }
    return Promise.reject(error);
  }
);

export const get = (url, params = {}) => api.get(url, { params });
export const post = (url, data = {}) => api.post(url, data);
export const put = (url, data = {}) => api.put(url, data);
export const del = (url) => api.delete(url);
export const patch = (url, data = {}) => api.patch(url, data);

export const uploadFile = (url, file, onProgress = null) => {
  const formData = new FormData();
  formData.append('file', file);
  return api.post(url, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const progress = (progressEvent.loaded / progressEvent.total) * 100;
        onProgress(progress);
      }
    },
  });
};

export const uploadMultipleFiles = (url, files, onProgress = null) => {
  const formData = new FormData();
  files.forEach((file) => formData.append('files', file));
  return api.post(url, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: (progressEvent) => {
      if (onProgress) {
        const progress = (progressEvent.loaded / progressEvent.total) * 100;
        onProgress(progress);
      }
    },
  });
};

export default api;
`
    );
  }

  if (answers.features.includes('dexie')) {
    await fs.mkdir(path.join(srcDir, 'db'), { recursive: true });
    await fs.writeFile(
      path.join(srcDir, 'db', 'database.js'),
      `import Dexie from 'dexie';

const db = new Dexie('HiworldVite');
db.version(1).stores({
  posts: '++id, title, content, createdAt',
  counter: '++id, count'
});

export default db;
`
    );
  }
}

async function updateMainJs(srcDir, importLine, useLine) {
  const mainPath = path.join(srcDir, 'main.js');
  let mainContent = await fs.readFile(mainPath, 'utf-8');

  if (importLine.includes('router') || importLine.includes('pinia')) {
    const hasRouter = mainContent.includes('router');
    const hasPinia = mainContent.includes('pinia');
    let imports = `import { createApp } from 'vue'\nimport App from './App.vue'\nimport './style.css'\n`;
    let uses = '';

    if (importLine.includes('router')) {
      imports += `import router from './router';\n`;
      uses += `app.use(router);\n`;
    }
    if (importLine.includes('pinia')) {
      imports += `import { createPinia } from 'pinia';\n`;
      uses += `app.use(createPinia());\n`;
    }
    if (hasRouter && !importLine.includes('router')) {
      imports += `import router from './router';\n`;
      uses += `app.use(router);\n`;
    }
    if (hasPinia && !importLine.includes('pinia')) {
      imports += `import { createPinia } from 'pinia';\n`;
      uses += `app.use(createPinia());\n`;
    }
    mainContent = `${imports}\nconst app = createApp(App)\n${uses}app.mount('#app')\n`;
  } else {
    mainContent = importLine + mainContent;
    mainContent = mainContent.replace('app.mount', `${useLine}\napp.mount`);
  }

  await fs.writeFile(mainPath, mainContent);
}